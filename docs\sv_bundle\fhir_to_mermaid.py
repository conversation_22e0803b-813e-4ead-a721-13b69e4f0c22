#!/usr/bin/env python3
"""
FHIR Bundle to Mermaid Diagram Converter
Usage: python fhir_to_mermaid.py input.json output.mmd
"""

import json
import sys
import os
import re
from datetime import datetime

def sanitize_id(resource_id):
    """Sanitize resource ID for use as Mermaid node identifier"""
    # Replace hyphens and other special characters with underscores
    return re.sub(r'[^a-zA-Z0-9_]', '_', resource_id)

def extract_display_info(resource):
    """Extract key information for display in the node"""
    resource_type = resource.get('resourceType', 'Unknown')
    resource_id = resource.get('id', 'no-id')
    
    info = {
        'type': resource_type,
        'id': resource_id,
        'display_lines': []
    }
    
    # Resource-specific information extraction
    if resource_type == 'Bundle':
        bundle_type = resource.get('type', 'unknown')
        timestamp = resource.get('timestamp', '')
        if timestamp:
            date_part = timestamp.split('T')[0]
            info['display_lines'] = [f"Type: {bundle_type}", f"Date: {date_part}"]
        else:
            info['display_lines'] = [f"Type: {bundle_type}"]
            
    elif resource_type == 'Patient':
        # Extract name
        name = "Unknown"
        if 'name' in resource and resource['name']:
            name_obj = resource['name'][0]
            if 'text' in name_obj:
                name = name_obj['text']
            elif 'family' in name_obj or 'given' in name_obj:
                family = name_obj.get('family', '')
                given = ' '.join(name_obj.get('given', []))
                name = f"{family}, {given}".strip(', ')
        
        # Extract identifier (like personal number)
        identifier = ""
        if 'identifier' in resource and resource['identifier']:
            identifier = resource['identifier'][0].get('value', '')
        
        info['display_lines'] = [name]
        if identifier:
            info['display_lines'].append(identifier)
            
    elif resource_type == 'Organization':
        name = resource.get('name', 'Unknown Organization')
        # Extract identifier
        identifier = ""
        if 'identifier' in resource and resource['identifier']:
            identifier = resource['identifier'][0].get('value', '')
        
        info['display_lines'] = [name]
        if identifier:
            info['display_lines'].append(f"ID: {identifier}")
            
    elif resource_type == 'Composition':
        title = resource.get('title', 'Unknown Document')
        status = resource.get('status', 'unknown')
        info['display_lines'] = [title, f"Status: {status}"]
        
    elif resource_type == 'ServiceRequest':
        status = resource.get('status', 'unknown')
        # Extract code display
        code_display = "Unknown Service"
        if 'code' in resource:
            if 'text' in resource['code']:
                code_display = resource['code']['text']
            elif 'coding' in resource['code'] and resource['code']['coding']:
                code_display = resource['code']['coding'][0].get('display', 'Unknown Service')
        
        info['display_lines'] = [code_display, f"Status: {status}"]
        
    elif resource_type == 'DiagnosticReport':
        status = resource.get('status', 'unknown')
        # Extract category
        category = "Unknown Category"
        if 'category' in resource and resource['category']:
            if 'coding' in resource['category'][0] and resource['category'][0]['coding']:
                category = resource['category'][0]['coding'][0].get('display', 'Unknown Category')
        
        info['display_lines'] = [f"{category} Report", f"Status: {status}"]
        
    elif resource_type == 'Observation':
        status = resource.get('status', 'unknown')
        category = "Laboratory"
        if 'category' in resource and resource['category']:
            if 'coding' in resource['category'][0] and resource['category'][0]['coding']:
                category = resource['category'][0]['coding'][0].get('display', 'Laboratory')
        
        info['display_lines'] = [f"{category} Result", f"Status: {status}"]
        
    elif resource_type == 'Specimen':
        # Extract accession identifier
        accession = ""
        if 'accessionIdentifier' in resource:
            accession = resource['accessionIdentifier'].get('value', '')
        
        # Extract collection date
        collection_date = ""
        if 'collection' in resource and 'collectedDateTime' in resource['collection']:
            collection_date = resource['collection']['collectedDateTime'].split('T')[0]
        
        info['display_lines'] = []
        if accession:
            info['display_lines'].append(f"Accession: {accession}")
        if collection_date:
            info['display_lines'].append(f"Collected: {collection_date}")
            
    elif resource_type == 'Encounter':
        status = resource.get('status', 'unknown')
        # Extract class
        encounter_class = "Unknown"
        if 'class' in resource:
            encounter_class = resource['class'].get('display', resource['class'].get('code', 'Unknown'))
        
        info['display_lines'] = [encounter_class, f"Status: {status}"]
        
    elif resource_type == 'PractitionerRole':
        # Extract practitioner display
        practitioner = "Unknown Practitioner"
        if 'practitioner' in resource:
            practitioner = resource['practitioner'].get('display', 
                          resource['practitioner'].get('identifier', {}).get('value', 'Unknown'))
        
        info['display_lines'] = [f"Practitioner: {practitioner}"]
    
    return info

def extract_references(resource):
    """Extract all references from a resource with their context"""
    references = []
    
    def find_references(obj, path=""):
        if isinstance(obj, dict):
            for key, value in obj.items():
                if key == 'reference' and isinstance(value, str):
                    # Extract the relationship type from the path
                    relation_type = path.rstrip('.').split('.')[-1] if path else key
                    references.append((relation_type, value))
                else:
                    find_references(value, path + key + ".")
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                find_references(item, path)
    
    find_references(resource)
    return references

def generate_mermaid_node(resource_info, alias):
    """Generate Mermaid node definition"""
    lines = [resource_info['type']]
    lines.append(resource_info['id'][:8] + "...")  # Shortened ID
    lines.extend(resource_info['display_lines'])
    
    # Escape quotes and format for Mermaid
    formatted_lines = []
    for line in lines:
        # Escape quotes and limit length
        line = str(line).replace('"', '\\"')
        if len(line) > 40:
            line = line[:37] + "..."
        formatted_lines.append(line)
    
    content = "<br/>".join(formatted_lines)
    return f'{alias}["{content}"]'

def convert_fhir_to_mermaid(json_file, output_file):
    """Convert FHIR Bundle JSON to Mermaid diagram"""
    
    # Read JSON file
    with open(json_file, 'r', encoding='utf-8') as f:
        bundle = json.load(f)
    
    # Start Mermaid content
    mermaid_content = []
    mermaid_content.append('graph TD')
    
    # Generate bundle node
    bundle_info = extract_display_info(bundle)
    bundle_alias = 'Bundle'
    mermaid_content.append(f'    {generate_mermaid_node(bundle_info, bundle_alias)}')
    mermaid_content.append('')
    
    # Process entries
    resource_aliases = {}
    resources = []
    
    if 'entry' in bundle:
        for i, entry in enumerate(bundle['entry']):
            if 'resource' in entry:
                resource = entry['resource']
                resource_type = resource.get('resourceType', 'Unknown')
                resource_id = resource.get('id', f'resource_{i}')
                
                # Create unique alias
                alias = f"{resource_type}"
                if alias in [r[2] for r in resources]:  # If alias already exists
                    alias = f"{resource_type}{i}"
                
                resource_aliases[resource_id] = alias
                resource_info = extract_display_info(resource)
                resources.append((resource, resource_info, alias))
                
                # Generate node
                mermaid_content.append(f'    {generate_mermaid_node(resource_info, alias)}')
                mermaid_content.append('')
    
    # Add bundle containment relationships with different line styles
    mermaid_content.append('    %% Bundle contains all resources')
    line_styles = ['-.->|"contains"|', '==>|"contains"|', '-->|"contains"|']
    for i, (resource, resource_info, alias) in enumerate(resources):
        style = line_styles[i % 3]  # Cycle through line styles
        mermaid_content.append(f'    {bundle_alias} {style} {alias}')
    mermaid_content.append('')

    # Add resource relationships based on references with different line styles
    mermaid_content.append('    %% Resource relationships')
    relationship_styles = ['-.->|', '==>|', '-->|']
    link_counter = len(resources)  # Start after bundle containment links

    for resource, resource_info, alias in resources:
        references = extract_references(resource)
        for relation_type, ref_value in references:
            # Extract resource ID from reference
            if '/' in ref_value:
                ref_id = ref_value.split('/')[-1]
                if ref_id in resource_aliases:
                    target_alias = resource_aliases[ref_id]
                    style = relationship_styles[link_counter % 3]  # Cycle through styles
                    mermaid_content.append(f'    {alias} {style}"{relation_type}"| {target_alias}')
                    link_counter += 1
    
    mermaid_content.append('')

    # Add custom line colors and styles
    mermaid_content.append('    %% Custom line colors and styles')
    colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3',
              '#54a0ff', '#5f27cd', '#00d2d3', '#ff6348', '#2ed573', '#ffa502',
              '#3742fa', '#ff3838', '#2f3542', '#57606f', '#a4b0be']

    for i in range(link_counter):
        color = colors[i % len(colors)]
        if i % 3 == 0:  # Dashed lines
            mermaid_content.append(f'    linkStyle {i} stroke:{color},stroke-width:2px,stroke-dasharray: 5 5')
        elif i % 3 == 1:  # Thick lines
            mermaid_content.append(f'    linkStyle {i} stroke:{color},stroke-width:3px')
        else:  # Regular lines
            mermaid_content.append(f'    linkStyle {i} stroke:{color},stroke-width:2px')

    mermaid_content.append('')

    # Add styling for dark backgrounds
    # Collect all aliases by type for class definitions
    bundle_aliases = ['Bundle']
    patient_aliases = []
    clinical_aliases = []
    org_aliases = []
    observation_aliases = []

    for resource, resource_info, alias in resources:
        resource_type = resource_info['type']
        if resource_type == 'Patient':
            patient_aliases.append(alias)
        elif resource_type in ['Composition', 'ServiceRequest', 'Encounter', 'DiagnosticReport', 'Specimen']:
            clinical_aliases.append(alias)
        elif resource_type in ['Organization', 'PractitionerRole']:
            org_aliases.append(alias)
        elif resource_type == 'Observation':
            observation_aliases.append(alias)

    mermaid_content.extend([
        '    %% Styling for dark background',
        '    classDef bundleClass fill:#1e3a8a,stroke:#60a5fa,stroke-width:3px,color:#ffffff',
        '    classDef patientClass fill:#7c2d92,stroke:#c084fc,stroke-width:2px,color:#ffffff',
        '    classDef clinicalClass fill:#166534,stroke:#4ade80,stroke-width:2px,color:#ffffff',
        '    classDef orgClass fill:#ea580c,stroke:#fb923c,stroke-width:2px,color:#ffffff',
        '    classDef observationClass fill:#be185d,stroke:#f472b6,stroke-width:2px,color:#ffffff',
        '',
    ])

    # Add class assignments dynamically
    if bundle_aliases:
        mermaid_content.append(f'    class {",".join(bundle_aliases)} bundleClass')
    if patient_aliases:
        mermaid_content.append(f'    class {",".join(patient_aliases)} patientClass')
    if clinical_aliases:
        mermaid_content.append(f'    class {",".join(clinical_aliases)} clinicalClass')
    if org_aliases:
        mermaid_content.append(f'    class {",".join(org_aliases)} orgClass')
    if observation_aliases:
        mermaid_content.append(f'    class {",".join(observation_aliases)} observationClass')
    
    # Write to file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(mermaid_content))
    
    print(f"Mermaid diagram generated: {output_file}")
    print(f"Resources processed: {len(resources)}")

def main():
    if len(sys.argv) < 2 or len(sys.argv) > 3:
        print("Usage: python fhir_to_mermaid.py input.json [output.mmd]")
        print("Example: python fhir_to_mermaid.py bundle.json bundle_diagram.mmd")
        print("If output file is not provided, it will use the input filename with .mmd extension")
        sys.exit(1)

    input_file = sys.argv[1]

    # Use provided output file or generate from input file name
    if len(sys.argv) == 3:
        output_file = sys.argv[2]
    else:
        # Generate output filename by replacing extension with .mmd
        import os
        base_name = os.path.splitext(input_file)[0]
        output_file = base_name + '.mmd'
    
    if not os.path.exists(input_file):
        print(f"Error: Input file '{input_file}' not found")
        sys.exit(1)
    
    try:
        convert_fhir_to_mermaid(input_file, output_file)
        print(f"\nSuccess! Open '{output_file}' in VS Code and use a Mermaid preview extension to view the diagram.")
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
