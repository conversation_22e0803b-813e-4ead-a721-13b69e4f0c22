sequenceDiagram
  participant launch as <PERSON> on fhir /launch
  participant fhir as Fhir server
  participant auth as Autentication
  participant index as Smart on fhir /index

  note over launch: Providing iss,client_id,scope *launch knows iss,client_id,scope
  launch ->> fhir: Get Well-known
  launch ->> auth: Authenticate user
  auth ->> auth: Authorization based on scope
  auth ->> index: grant access token with practionerId
  auth ->> index: redirect
  note over index: Is Authenticated
  index ->> fhir: Get PractionerRoles
  index ->> index: list of PractionerRoles
  index ->> index: select PractionerRole
  index ->> index: ask for Patient ID
  index ->> fhir: Get Patient
  index ->> fhir: Get Practioner
  index ->> fhir: Get PractionerRole
  index ->> index: display data


