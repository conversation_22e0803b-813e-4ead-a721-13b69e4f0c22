import { render, screen, waitFor } from "@testing-library/react";
import smart from "fhirclient";
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import { FhirIndex } from "@/app/fhir-index/components/fhir-index";

// Mock the fhirclient library
vi.mock("fhirclient", () => {
	const mockPatientData = {
		resourceType: "Patient",
		id: "test-patient-id",
		name: [{ given: ["Test"], family: "Patient" }],
	};
	const mockMedicationData = [
		{ resourceType: "MedicationRequest", id: "med-1" },
	];

	const mockClient = {
		patient: {
			id: "test-patient-id",
			read: vi.fn().mockResolvedValue(mockPatientData),
		},
		request: vi.fn().mockResolvedValue(mockMedicationData),
	};

	return {
		default: {
			oauth2: {
				ready: vi.fn().mockResolvedValue(mockClient),
			},
		},
	};
});

describe("FhirIndex Component", () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	it("should initialize FHIR client and display data", async () => {
		render(<FhirIndex />);

		// Check loading state is shown initially
		expect(screen.getByText("Loading FHIR data ...")).toBeDefined();

		// Verify smart.oauth2.ready was called
		expect(smart.oauth2.ready).toHaveBeenCalled();

		// Wait for the component to finish loading
		await waitFor(() => {
			expect(screen.queryByText("Loading FHIR data ...")).toBeNull();
		});

		// Check that patient and medication data is displayed
		expect(screen.getByText("Patient Information")).toBeDefined();
		expect(screen.getByText("Medication Information")).toBeDefined();
	});

	it("should handle errors during FHIR client initialization", async () => {
		// Override the mock to simulate an error
		vi.mocked(smart.oauth2.ready).mockRejectedValueOnce(
			new Error("Failed to initialize FHIR client"),
		);

		render(<FhirIndex />);

		// Wait for the error to be displayed
		await waitFor(() => {
			expect(
				screen.getByText("Error: Failed to initialize FHIR client"),
			).toBeDefined();
		});
	});

	it("should call smart.oauth2.ready again when button is clicked", async () => {
		render(<FhirIndex />);

		// Wait for initial load to complete
		await waitFor(() => {
			expect(screen.queryByText("Loading FHIR data ...")).toBeNull();
		});

		// Initial call count should be 1
		expect(smart.oauth2.ready).toHaveBeenCalledTimes(1);

		// Find and click the button
		const button = screen.getByRole("button", { name: /get fhir data/i });
		button.click();

		// Verify smart.oauth2.ready was called again
		expect(smart.oauth2.ready).toHaveBeenCalledTimes(2);

		// Check that patient and medication data is still displayed
		expect(screen.getByText("Patient Information")).toBeDefined();
		expect(screen.getByText("Medication Information")).toBeDefined();
	});
});
