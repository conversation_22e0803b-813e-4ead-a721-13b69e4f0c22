
parameters:
  dockerfilePath: ''
  imageRepository: ''
  containerRegistry: ''
  tag: ''
  pushImage: true

steps:
  - task: Docker@2
    displayName: 'Build Docker image'
    inputs:
      command: 'build'
      repository: ${{ parameters.imageRepository }}
      dockerfile: ${{ parameters.dockerfilePath }}
      tags: |
        ${{ parameters.tag }}
        latest

  - ${{ if eq(parameters.pushImage, true) }}:
    - task: Docker@2
      displayName: 'Push Docker image'
      inputs:
        command: 'push'
        repository: ${{ parameters.imageRepository }}
        containerRegistry: ${{ parameters.containerRegistry }}
        tags: |
          ${{ parameters.tag }}
          latest

