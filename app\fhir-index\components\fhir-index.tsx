"use client";

import type { MedicationRequest } from "fhir/r4";
import smart from "fhirclient";
import type { fhirclient } from "fhirclient/lib/types";
import { useCallback, useEffect, useState } from "react";

export function FhirIndex() {
	const [patientData, setPatientData] =
		useState<fhirclient.FHIR.Patient | null>(null);
	const [medicationData, setMedicationData] =
		useState<MedicationRequest | null>(null);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const getFhirData = useCallback(async () => {
		setLoading(true);

		try {
			// Assuming smart is accessible here
			// If smart comes from props or context, add it to useCallback dependencies
			const client = await smart.oauth2.ready();
			const [patient, meds] = await Promise.all([
				client.patient.read(),
				client.request(`/MedicationRequest?patient=${client.patient.id}`, {
					resolveReferences: "medicationReference",
					pageLimit: 0,
					flat: true,
				}),
			]);

			setPatientData(patient);
			setMedicationData(meds);
		} catch (err) {
			console.error(err);
			setError(
				err instanceof Error ? err.message : "An unknown error occurred",
			);
			// No need for setLoading(false) here, finally block handles it
		} finally {
			setLoading(false);
		}
	}, []); // Dependencies for useCallback

	useEffect(() => {
		getFhirData();
	}, [getFhirData]);

	if (loading) {
		return <div>Loading FHIR data ...</div>;
	}

	if (error) {
		return <div>Error: {error}</div>;
	}

	return (
		<div className="flex flex-col">
			<div>index client components</div>
			<button
				type="button"
				className="bg-blue-400 p-2 w-fit active:bg-blue-500 cursor-pointer"
				onClick={() => getFhirData()}
			>
				get fhir data
			</button>
			<div className="flex flex-col gap-y-2">
				{patientData && (
					<div>
						<h3>Patient Information</h3>
						<pre>{JSON.stringify(patientData, null, 2)}</pre>
					</div>
				)}
				{medicationData && (
					<div>
						<h3>Medication Information</h3>
						<pre>{JSON.stringify(medicationData, null, 2)}</pre>
					</div>
				)}
			</div>
		</div>
	);
}
