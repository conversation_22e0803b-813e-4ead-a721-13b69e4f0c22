{"$schema": "https://biomejs.dev/schemas/2.2.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": true, "includes": ["**", "!**/node_modules", "!**/.next", "!**/.vscode", "!**/coverage", "!**/storybook-static", "!**/*.css", "!**/node_modules", "!**/pnpm_config_cache", "!**/.pnpm-store", "!**/$(pnpm_config_cache)"]}, "formatter": {"enabled": true, "indentStyle": "tab", "useEditorconfig": true}, "assist": {"actions": {"source": {"organizeImports": "on"}}}, "linter": {"enabled": true, "rules": {"recommended": true}, "includes": ["**", "!**/coverage/**", "!**/node_modules", "!**/pnpm_config_cache", "!**/.pnpm-store", "!**/$(pnpm_config_cache)"]}, "javascript": {"formatter": {"quoteStyle": "double"}}}