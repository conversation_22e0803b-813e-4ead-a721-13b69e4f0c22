import { render, screen } from "@testing-library/react";
import { expect, test } from "vitest";
import Home from "@/app/page";

test("Page", () => {
	render(<Home />);

	// Check for the main title text
	expect(screen.getByText("Välkommen till Labbsvar")).toBeDefined();

	// Check for the description text
	expect(screen.getByText("Noll läget, information om appen")).toBeDefined();

	// Check that the card title element exists (it's a div with data-slot="card-title")
	const cardTitle = screen.getByText("Välkommen till Labbsvar");
	expect(cardTitle.closest('[data-slot="card-title"]')).toBeDefined();
});
