# FHIR Bundle to Mermaid Converter

This Python script converts FHIR Bundle JSON files into Mermaid diagrams that visualize the relationships between FHIR resources.

## Usage

```bash
python fhir_to_mermaid.py input.json output.mmd
```

### Example
```bash
python fhir_to_mermaid.py "svensk/Bundle-377bf7b8-6d6f-3be5-799c-df2fec9e08f6.json" "my_diagram.mmd"
```

## Features

- **Automatic Resource Detection**: Identifies all FHIR resources in the bundle
- **Relationship Mapping**: Extracts and visualizes references between resources
- **Dark Theme Support**: Colors optimized for dark VS Code themes
- **Resource-Specific Information**: Shows relevant details for each resource type:
  - **Patient**: Name and identifier (personal number)
  - **Organization**: Name and organizational ID
  - **Composition**: Title and status
  - **ServiceRequest**: Service description and status
  - **DiagnosticReport**: Category and status
  - **Observation**: Category and status
  - **Specimen**: Accession number and collection date
  - **Encounter**: Class and status
  - **PractitionerRole**: Practitioner information

## Viewing the Output

1. Install a Mermaid extension in VS Code:
   - **Mermaid Preview** by vstirbu (recommended)
   - **Markdown Preview Mermaid Support** by Matt Bierner

2. Open the generated `.mmd` file in VS Code

3. Preview the diagram:
   - Right-click and select "Preview Mermaid"
   - Use `Ctrl+Shift+P` and type "Mermaid Preview"
   - Look for preview button in editor toolbar

## Color Scheme (Dark Background)

- **Bundle**: Dark blue with white text
- **Patient**: Dark purple with white text  
- **Clinical Resources**: Dark green with white text (Composition, ServiceRequest, Encounter, DiagnosticReport, Specimen)
- **Organizations**: Dark orange with white text
- **Observations**: Dark pink with white text

## Requirements

- Python 3.6+
- No additional dependencies required (uses only standard library)

## Output Format

The script generates a Mermaid flowchart (graph TD) showing:
1. All resources as nodes with key information
2. Bundle containment relationships
3. Inter-resource references with relationship labels
4. Color-coded styling for different resource types
