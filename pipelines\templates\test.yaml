# commented out e2e testing. need a running server for that.
parameters:
  runE2E: false

steps:
  - script: |
      pnpm run test
    displayName: 'Run Vitest unit tests'

  - script: |
      pnpm test:coverage
    displayName: 'Run tests with coverage'

  - task: PublishTestResults@2
    displayName: 'Publish test results'
    inputs:
      testResultsFormat: 'JUnit'
      testResultsFiles: '**/junit.xml'
      mergeTestResults: true
      testRunTitle: 'Component Tests'
    condition: succeededOrFailed()

  - task: PublishCodeCoverageResults@2
    displayName: 'Publish code coverage'
    inputs:
      codeCoverageTool: 'Cobertura'
      summaryFileLocation: '$(System.DefaultWorkingDirectory)/coverage/cobertura-coverage.xml'
      reportDirectory: '$(System.DefaultWorkingDirectory)/coverage'
    condition: succeededOrFailed()


  # - ${{ if eq(parameters.runE2E, true) }}:
  #   - script: |
  #       npx playwright install --with-deps
  #     displayName: 'Install Playwright dependencies'

  #   - script: |
  #       pnpm run e2e
  #     displayName: 'Run Playwright E2E tests'

  #   - task: PublishTestResults@2
  #     condition: succeededOrFailed()
  #     inputs:
  #       testResultsFormat: 'JUnit'
  #       testResultsFiles: '**/test-results.xml'
  #       mergeTestResults: true
  #       testRunTitle: 'E2E Tests'
