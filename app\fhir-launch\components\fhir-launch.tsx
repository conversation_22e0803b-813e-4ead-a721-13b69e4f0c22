"use client";

import { oauth2 as smart } from "fhirclient";
import type { fhirclient as fhirtypes } from "fhirclient/lib/types";

export function FhirLaunch({
	fhirConfiguration,
	text,
}: {
	fhirConfiguration: fhirtypes.AuthorizeParams;
	text: string;
}) {
	function startLaunch() {
		smart.authorize(fhirConfiguration);
	}
	return (
		<div className="flex flex-col">
			<button
				type="button"
				className="bg-blue-400 p-2 w-fit active:bg-blue-500 cursor-pointer"
				onClick={() => startLaunch()}
			>
				{text}
			</button>
		</div>
	);
}
