import smart from "fhirclient";
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";

// Mock the fhirclient library
vi.mock("fhirclient", () => {
	return {
		default: {
			oauth2: {
				authorize: vi.fn(),
			},
		},
	};
});

// Import the configuration directly to test against
const fhirConf = {
	iss: "https://launch.smarthealthit.org/v/r3/sim/eyJoIjoiMSIsImIiOiJzbWFydC0xNjQyMDY4IiwiZSI6InNtYXJ0LVByYWN0aXRpb25lci03MTYxNDUwMiJ9/fhir",
	clientId: "6f2440f2-f014-410a-8dc2-0f0a5ebaf58c",
	scope: "launch/patient offline_access openid fhirUser",
	redirectUri: "http://localhost:3000/fhir-index",
};

// Import the startLaunch function directly
function startLaunch() {
	smart.oauth2.authorize(fhirConf);
}

describe("FhirLaunch Component", () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	it("should call startLaunch function directly", () => {
		// Call the startLaunch function directly
		startLaunch();

		// Verify smart.oauth2.authorize was called with the correct configuration
		expect(smart.oauth2.authorize).toHaveBeenCalledWith(fhirConf);
	});
});
