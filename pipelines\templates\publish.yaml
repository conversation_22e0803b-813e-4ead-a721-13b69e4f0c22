# templates/publish.yaml
parameters:
  artifactName: 'nextjs-app'
  updateChangeset: false

steps:
  # This step conditionally calls the changeset template
  # It checks if updateChangeset is true AND if the build is from the main branch
  - ${{ if and(eq(parameters.updateChangeset, true), eq(variables['Build.SourceBranch'], 'refs/heads/main')) }}:
    - template: changeset.yaml
      parameters:
        commitMessage: 'Updated changeset [skip ci]'
        userEmail: '<EMAIL>'
        userName: 'Azure Pipeline'

  - script: |
      mkdir -p $(Build.ArtifactStagingDirectory)/publish
    displayName: 'Create publish directory'

  - script: |
      cp -r .next $(Build.ArtifactStagingDirectory)/publish/
      cp -r public $(Build.ArtifactStagingDirectory)/publish/
      cp package.json $(Build.ArtifactStagingDirectory)/publish/
      cp pnpm-lock.yaml $(Build.ArtifactStagingDirectory)/publish/
      cp next.config.ts $(Build.ArtifactStagingDirectory)/publish/
    displayName: 'Copy Next.js build artifacts'

  - task: PublishBuildArtifacts@1
    displayName: 'Publish artifacts'
    inputs:
      PathtoPublish: '$(Build.ArtifactStagingDirectory)/publish'
      ArtifactName: ${{ parameters.artifactName }}
      publishLocation: 'Container'
