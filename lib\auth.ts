import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { nextCookies } from "better-auth/next-js";
import type { MicrosoftOptions } from "better-auth/social-providers";
import { db } from "@/db";
import { schema } from "@/db/schema/auth-schema";

const _entraIDConfig: MicrosoftOptions = {
	clientId: process.env.AUTH_AZURE_AD_ID as string,
	clientSecret: process.env.AUTH_AZURE_AD_SECRET as string,

	// Optional
	tenantId: process.env.AUTH_AZURE_AD_TENANT_ID as string,
	prompt: "login", // Forces account selection use select_account
	authority: "https://login.microsoftonline.com", // Authentication authority URL
	scope: ["email", "openid", "profile", "User.Read"],
};

export const auth = betterAuth({
	appName: "better-labdata",
	emailAndPassword: {
		enabled: false,
	},
	database: drizzleAdapter(db, {
		provider: "sqlite", // or "mysql", "sqlite"
		schema: schema,
	}),
	socialProviders: {
		microsoft: _entraIDConfig,
	},
	plugins: [nextCookies()], //nextCookies() need to be last
});
