import Link from "next/link";
import { urls } from "../_lib/constants";
import { FhirIndex } from "./components/fhir-index";
// Server component
export default async function FhirIndexPage() {
	const myurls = urls.filter(
		(u) =>
			u.title.toLowerCase().includes("launch") ||
			u.title.toLowerCase().includes("home"),
	);
	return (
		<div>
			<h1>Fhir client index</h1>
			<ul>
				{myurls.map((item) => (
					<li key={item.url}>
						<Link className="underline" href={item.url}>
							{item.title}
						</Link>
					</li>
				))}
			</ul>
			<FhirIndex />
		</div>
	);
}
