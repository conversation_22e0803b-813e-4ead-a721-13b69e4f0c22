# Smart on FHIR client in Nextjs

this webapp is a solution for communicating with FHIR server.

This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

### Technologies

- [Node.js 22](https://nodejs.org/en)

- [Next.js 15](https://nextjs.org/docs)

- [Tailwind CSS 4](https://tailwindcss.com/)

- [Fhirclient 2](https://github.com/smart-on-fhir/client-js)

### Testing

For unit testing vitest.
For e2e testing playwright.
For UX component testing storybook.
For linting Biome (and eslint)

- [Vitest](https://vitest.dev/)

- [Playwright](https://playwright.dev/)

- [Storybook](https://storybook.js.org/)

- [Biome](https://biomejs.dev/)

## Getting Started

First, run the development server:

```bash
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Other Runners
There are also other runners for diffrent tasks.

- dev ,start dev env
- build , build the application production
- start , run the built production application
- lint , lint the code using next eslint *slow
- lint:biome ,lint using biome *faster
- test ,run unit tests using vitest
- test:coverage ,run unit tests coverage using vitest
- storybook , start the storybook UI/UX testing env
- build-storybook ,build a storybook (not needed)
- e2e , run playwright tests (ui interaction testing)
- typecheck , typechecking, faster than build for checking for errors
- check:biome ,lint and formatter checking


## Docker
There is a dockerfile with docker configurations
it used port 3000

### run locally image
build the docker container
```bash
docker build -t fhir-client .
```
run the docker container
```bash
docker run -p 3000:3000 fhir-client
```
