import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import Link from "next/link";
import { LogoutBtn } from "@/components/logout";
import { urls } from "./_lib/constants";

const geistSans = Geist({
	variable: "--font-geist-sans",
	subsets: ["latin"],
});

const geistMono = Geist_Mono({
	variable: "--font-geist-mono",
	subsets: ["latin"],
});

export const metadata: Metadata = {
	title: "Region Skåne labbsvar",
	description: "Region Skåne labbsvar",
};

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang="en">
			<body
				className={`${geistSans.variable} ${geistMono.variable} antialiased`}
			>
				<header className="flex justify-between">
					<details>
						<summary className="border p-2"><PERSON><PERSON><PERSON><PERSON> till sidor</summary>
						<ul className="space-y-1">
							{urls.map((item) => {
								return (
									<li key={item.url}>
										<Link className="underline" href={item.url}>
											{item.title}
										</Link>
									</li>
								);
							})}
						</ul>
					</details>
					<LogoutBtn />
				</header>
				{children}
			</body>
		</html>
	);
}
