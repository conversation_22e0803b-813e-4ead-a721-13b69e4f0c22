import type { NextConfig } from "next";

const nextConfig: NextConfig = {
	/* config options here */
	eslint: { ignoreDuringBuilds: true },
	output: process.env.BUILD_STANDALONE === "true" ? "standalone" : undefined,
	async rewrites() {
		return [
			{
				source: "/standalone",
				destination: "/standalone/1",
			},
			{
				source: "/api/auth/error",
				destination: "/error",
			}, //due to better-auth on api error isnt working correcly
		];
	},
};

export default nextConfig;
