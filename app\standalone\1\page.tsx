import { headers } from "next/headers";
import { redirect } from "next/navigation";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { auth } from "@/lib/auth";

async function StandalonePageV1() {
	const session = await Promise.all([
		auth.api.getSession({
			headers: await headers(),
		}),
		auth.api.listSessions({
			headers: await headers(),
		}),
		await auth.api.getAccessToken({
			headers: await headers(), // some endpoints might require headers
			body: {
				providerId: "microsoft",
			},
		}),
	]).catch((e) => {
		console.log(e);
		throw redirect("/login");
	});

	return (
		<div className="bg-muted flex min-h-svh flex-col items-center justify-center gap-6 p-6 md:p-10">
			<div className="flex w-full max-w-full flex-col gap-6">
				<Card>
					<CardHeader className="text-center">
						<CardTitle className="text-xl">Standalone page 1</CardTitle>
						<CardDescription>Endast för självständigt läge</CardDescription>
					</CardHeader>
					<CardContent>
						<details>
							<summary>Session Details</summary>
							<div>
								current session:{" "}
								<pre>
									{session
										? JSON.stringify(session, null, 2)
										: "No session, not logged in"}
								</pre>
							</div>
						</details>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}

export default StandalonePageV1;
