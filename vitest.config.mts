import react from "@vitejs/plugin-react";
import tsconfigPaths from "vite-tsconfig-paths";
import { defineConfig } from "vitest/config";

export default defineConfig({
	plugins: [tsconfigPaths(), react()],
	test: {
		environment: "jsdom",
		exclude: ["**/playwright_tests/**", "**/node_modules/**", "**/dist/**"],
		reporters: ["default", "junit"],
		outputFile: {
			junit: "./junit.xml",
		},
		coverage: {
			provider: "v8",
			reporter: ["text", "json", "html", "cobertura"],
			reportsDirectory: "./coverage",
		},
	},
});
