# Exclude Next.js build output (generated inside the container)
.next/

# Exclude Node.js dependencies (installed inside the container)
node_modules/

# Exclude development and testing related files and directories
.storybook/
.vscode/
playwright-report/
playwright_tests/ # Added based on your directory listing
test-results/
__tests__/ # Added based on your directory listing
stories/ # If only for Storybook and not part of app source

# Exclude configuration files not needed at runtime
#biome.json
#eslint.config.mjs
playwright.config.ts
#vitest.config.mts

# Exclude build-specific files
tsconfig.tsbuildinfo
# Keep tsconfig.json and next-env.d.ts as they are needed for the build

# Exclude package manager workspace file if not needed for building a single app
pnpm-workspace.yaml # Added based on your directory listing

# Exclude miscellaneous files and folders based on your gitignore and directory listing
.DS_Store
*.pem
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
/coverage # From gitignore
/out/ # From gitignore
/build # From gitignore
.vercel # From gitignore
/blob-report/ # From gitignore (Playwright)
/playwright/.cache/ # From gitignore (Playwright)

# Optional: Exclude environment files unless you need to copy them specifically
.env*

# Optional: Exclude editor/IDE specific files
*.swp
*.swo

.git
README.md
