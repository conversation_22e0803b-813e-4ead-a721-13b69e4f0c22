"use server";
import type { fhirclient as fhirtypes } from "fhirclient/lib/types";

/**
 * function to populate fhir configuration from environment variables
 * @returns fhir configurations
 */
export async function getFhirParams(
	mode: "local" | "external" | null = "external",
): Promise<fhirtypes.AuthorizeParams> {
	if (mode === "local") {
		return {
			iss: process.env.LOCAL_IIS,
			clientId: process.env.LOCAL_CLIENTID,
			scope: process.env.LOCAL_SCOPE,
		};
	}

	return {
		iss: process.env.IIS,
		clientId: process.env.CLIENTID,
		scope: process.env.SCOPE,
	};
}
