sequenceDiagram
  participant app as Smart app
  participant auth as EHR authorization server

  Note left of app: Request authorization
  app ->> auth: redirect {ehr authorization url}
  Note over auth: On approval
  auth ->> app: redirect {app url}?code=123&scope...
  Note left of app: Exchange code for access token
  app ->> auth: POST {token url} grant
  Note over auth: Authenticate app
  Note over auth: Issues anew token with context. with patient, practioner
  auth ->> app: Access token response
  