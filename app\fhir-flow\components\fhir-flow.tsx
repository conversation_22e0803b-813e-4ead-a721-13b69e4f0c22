"use client";

import type { AllergyIntolerance, Bundle, Patient } from "fhir/r4";
import { oauth2 as smart } from "fhirclient";
import type Client from "fhirclient/lib/Client";
import type { fhirclient } from "fhirclient/lib/types";
import { useCallback, useEffect, useId, useRef, useState } from "react";

type FhirFlowCompProps = {
	fhirConfiguration: fhirclient.AuthorizeParams;
	fhirResourcesNames?: string[];
};

export function FhirFlowComp({
	fhirConfiguration,
	fhirResourcesNames,
}: FhirFlowCompProps) {
	const [patientData, setPatientData] =
		useState<fhirclient.FHIR.Patient | null>(null);
	const [error, setError] = useState<string | null>(null);
	const [loading, setLoading] = useState<boolean>(true);
	const [patiens, setPatiens] = useState<Bundle<Patient> | null>(null);
	const [resourceData, setResourceData] = useState<unknown | null>(null);
	const [allergyIntoleranceData, setAllergyIntoleranceData] =
		useState<Bundle<AllergyIntolerance> | null>(null);

	const [selectedTab, setSelectedTab] = useState<string | null>(
		"select-patient",
	);

	const selectPatientID = useId();
	const patientInfoID = useId();
	const allergyID = useId();
	const fhirTestingID = useId();
	const fhirserverResourcesChoiceID = useId();

	const [theClient, setTheClient] = useState<Client | null>(null);

	const inputResource = useRef<HTMLInputElement>(null);
	/**
	 * - get practicitoner
	 * - get patiesns
	 * - get patiens Allergy
	 */

	/***
	 * Does a complete call to fhir server. both launch and get the data
	 */
	const initFhirClient = useCallback(async () => {
		try {
			//sets up the fhir client with fhir server url (iss) and clientId this app.
			const client = await smart.init(fhirConfiguration);
			//make client avaible for button
			setTheClient(client);

			//Get all patient on fhir server
			const patiens = await client.request<Bundle<Patient>>("Patient");

			setPatiens(patiens);

			setLoading(false);
		} catch (err) {
			console.error(err);
			setError(
				err instanceof Error ? err.message : "An unknown error occurred",
			);
			setLoading(false);
		}
	}, [fhirConfiguration]);

	//After page is loaded, load practitioners
	useEffect(() => {
		initFhirClient();
	}, [initFhirClient]);

	if (loading) {
		return (
			<div>
				<div>Loading FHIR data...</div>
				<div>With this configuration</div>
				<pre>{JSON.stringify(fhirConfiguration, null, 2)}</pre>
			</div>
		);
	}

	if (error) {
		return (
			<div>
				<div>Failed to load with this configuration</div>
				<pre>{JSON.stringify(fhirConfiguration, null, 2)}</pre>
				<div>Error: {error}</div>);
			</div>
		);
	}

	async function handlePatient(id: string | undefined): Promise<void> {
		//Get the choosen patient
		const data = await theClient?.request(`Patient/${id}`);
		const allergy = await theClient?.request(
			`AllergyIntolerance?patient=${id}`,
		);
		setAllergyIntoleranceData(allergy);
		setPatientData(data);
	}

	async function getResourceData() {
		const resourceName = inputResource.current?.value;
		console.log("get r", resourceName);
		if (!resourceName || resourceName === "") {
			console.log("empty null");
		} else {
			const res = await theClient?.request(resourceName);
			console.log("res", res);
			setResourceData(res?.entry);
		}
	}

	return (
		<div className="  container mx-auto px-auto ">
			<div className="pb-8">
				<h4 className="text-lg">Titta på patient</h4>
				<div>
					<span>Namn:</span>
					{!patientData && "Ingen patient vald"}
					{`${patientData?.name[0]?.text ?? ""}`}
					{patientData?.name[0]?.family &&
						`${patientData?.name[0]?.family ?? ""}, ${patientData?.name[0]?.given?.join(" ") ?? ""}`}
				</div>
				<button
					type="button"
					className="border p-2 my-3"
					onClick={() => {
						setPatientData(null);
						setAllergyIntoleranceData(null);
					}}
				>
					Nollställ
				</button>
			</div>
			<fieldset
				className="flex gap-x-4 py-6"
				onChange={(e) => {
					const el = e.target as HTMLInputElement;
					setSelectedTab(el.value);
				}}
			>
				<div>
					<input
						type="radio"
						id={selectPatientID}
						name="selected-tab"
						value="select-patient"
						className="opacity-0 peer"
					/>
					<label
						htmlFor={selectPatientID}
						className="peer-checked:bg-blue-50 border p-2 rounded-full hover:bg-amber-50"
					>
						Välj Patient
					</label>
				</div>

				<div>
					<input
						type="radio"
						id={patientInfoID}
						name="selected-tab"
						value="patient-info"
						className="opacity-0 peer"
					/>
					<label
						htmlFor={patientInfoID}
						className="peer-checked:bg-blue-50 border p-2 rounded-full hover:bg-amber-50"
					>
						Patient information
					</label>
				</div>

				<div>
					<input
						type="radio"
						id={allergyID}
						name="selected-tab"
						value="allergy"
						className="opacity-0 peer"
					/>
					<label
						htmlFor={allergyID}
						className="peer-checked:bg-blue-50 border p-2 rounded-full hover:bg-amber-50"
					>
						AllergyIntolerance
					</label>
				</div>
				<div>
					<input
						type="radio"
						id={fhirTestingID}
						name="selected-tab"
						value="fhir-testing"
						className="opacity-0 peer"
					/>
					<label
						htmlFor={fhirTestingID}
						className="peer-checked:bg-blue-50 border p-2 rounded-full hover:bg-amber-50"
					>
						FHIR api testning
					</label>
				</div>
			</fieldset>

			<div className="flex gap-y-4 flex-col py-3">
				{selectedTab === "select-patient" && (
					<div>
						<h4>Patienter</h4>
						<div>
							<ul className="flex flex-col gap-y-2">
								{patiens?.entry?.map((patient) => (
									<li
										key={patient.fullUrl}
										className="flex  justify-between border-b"
									>
										<div className="py-1">
											<div>
												<span>Namn: </span>
												{patient?.resource?.name?.[0].text ?? ""}
												{patient?.resource?.name?.[0].family &&
													`${patient.resource.name?.[0].family ?? ""}, ${patient.resource.name[0].given?.join(" ") ?? ""}`}
											</div>
											<div>id: {patient?.resource?.id}</div>
										</div>
										<form className="flex flex-col items-end pr-2">
											<button
												type="button"
												className="border p-2"
												onClick={(e) => {
													e.preventDefault();
													handlePatient(patient.resource?.id);
												}}
											>
												Välj
											</button>
										</form>
									</li>
								))}
							</ul>
						</div>
					</div>
				)}

				{selectedTab === "patient-info" && (
					<div>
						<details>
							<summary>
								{!patientData && "Ingen patient vald"}
								{`${patientData?.name[0]?.text ?? ""}`}
								{patientData?.name[0]?.family &&
									`${patientData?.name[0]?.family ?? ""}, ${patientData?.name[0]?.given?.join(" ") ?? ""}`}
							</summary>
							<pre>{JSON.stringify(patientData, null, 2)}</pre>
						</details>
					</div>
				)}
				{selectedTab === "allergy" && (
					<div>
						<details>
							<summary>
								{!patientData ? "Ingen patient vald" : "Allergier"}
							</summary>
							<pre>{JSON.stringify(allergyIntoleranceData, null, 2)}</pre>
						</details>
					</div>
				)}
			</div>
			{selectedTab === "fhir-testing" && (
				<div>
					<h4 className="text-2xl">
						Lista på tillgänlig infromation från fhir
					</h4>
					<div className="py-4 flex flex-col">
						<label htmlFor="fhirserver-resources-choice">
							välj en Fhir resurs:
						</label>
						<input
							ref={inputResource}
							className="border p-1"
							list="fhirserver-resources-list"
							id={fhirserverResourcesChoiceID}
							name="fhirserver-resources-choice"
						/>

						<datalist id={fhirserverResourcesChoiceID}>
							{fhirResourcesNames?.map((rname) => {
								return <option key={rname} value={rname} />;
							})}
						</datalist>
						<button
							type="button"
							className="border p-1"
							onClick={(_e) => getResourceData()}
						>
							get resource
						</button>

						<div className="py-3">
							<pre>{JSON.stringify(resourceData, null, 2)}</pre>
						</div>
					</div>
				</div>
			)}
		</div>
	);
}
