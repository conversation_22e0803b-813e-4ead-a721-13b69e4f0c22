parameters:
  buildArguments: ''

steps:
  - script: |
      pnpm run typecheck
    displayName: 'Run TypeScript type checking'

  - script: |
      pnpm run lint
    displayName: 'Run Biome linting'

  - task: Cache@2
    displayName: 'Cache .next/cache'
    inputs:
      key: next | $(Agent.OS) | pnpm-lock.yaml
      path: '$(System.DefaultWorkingDirectory)/.next/cache'

  - script: |
      pnpm run build ${{ parameters.buildArguments }}
    displayName: 'Build Next.js application'

  - script: |
      pnpm exec next telemetry disable
    displayName: 'Disable Next.js telemetry'
