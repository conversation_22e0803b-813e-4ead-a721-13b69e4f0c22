parameters:
  commitMessage: 'Updated changeset [skip ci]'
  userEmail: '<EMAIL>'
  userName: 'Azure Pipeline'

# steps:
#   - script: |
#       pnpm exec changeset version
#       git config user.email "${{ parameters.userEmail }}"
#       git config user.name "${{ parameters.userName }}"
#       git add .
#       git commit -m "${{ parameters.commitMessage }}"
#       git push origin HEAD:$(Build.SourceBranchName)
#     displayName: 'Update changeset'


steps:
  - task: Bash@3
    name: version_packages
    displayName: 'Version Packages (Changesets)'
    condition: succeeded()
    env:
      CI: true
    inputs:
      targetType: 'inline'
      script: |
        set -x

        # Checkout the right branch
        git fetch --all
        git switch $(basename $(Build.SourceBranch))

        # Define your designated prerelease branches here
        PRERELEASE_BRANCHES=("alpha" "beta" "next") # Add/remove branches as needed

        # Get target branch name
        TARGET_BRANCH=$(basename $(Build.SourceBranch))

        # Check if currently in Changesets prerelease mode and get existing tag
        CURRENT_PRE_TAG=""
        if [ -e .changeset/pre.json ]; then
          CURRENT_PRE_TAG=$(jq -r .tag .changeset/pre.json)
          echo "Detected existing prerelease tag: $CURRENT_PRE_TAG in .changeset/pre.json"
        else
          echo "No .changeset/pre.json found. Not currently in prerelease mode."
        fi

        # Check if the target branch is one of the designated prerelease branches
        IS_PRERELEASE_BRANCH=false
        for branch in "${PRERELEASE_BRANCHES[@]}"; do
          if [ "$TARGET_BRANCH" == "$branch" ]; then
            IS_PRERELEASE_BRANCH=true
            break
          fi
        done

        # Manage Changesets prerelease mode based on target branch
        if [ "$IS_PRERELEASE_BRANCH" == true ]; then
          # If on a prerelease branch, ensure we are in prerelease mode with the correct tag
          if [ "$CURRENT_PRE_TAG" != "$TARGET_BRANCH" ]; then
            if [ -n "$CURRENT_PRE_TAG" ]; then
              echo "Exiting old prerelease mode ($CURRENT_PRE_TAG) before entering $TARGET_BRANCH mode."
              pnpm exec changeset pre exit
            fi
            echo "Entering prerelease mode for tag '$TARGET_BRANCH'."
            pnpm exec changeset pre enter $TARGET_BRANCH
          else
            echo "Already correctly in prerelease mode for tag '$TARGET_BRANCH'."
          fi
        else
          # If not on a prerelease branch, ensure we are NOT in prerelease mode
          if [ -n "$CURRENT_PRE_TAG" ]; then
            echo "Exiting prerelease mode ($CURRENT_PRE_TAG) as branch '$TARGET_BRANCH' is not a prerelease branch."
            pnpm exec changeset pre exit
          else
            echo "Not in prerelease mode, which is correct for branch '$TARGET_BRANCH'."
          fi
        fi

        # --- Changeset Versioning ---

        # Check if there are changesets that need to be versioned
        echo "Checking for changesets..."
        pnpm exec changeset status --verbose
        CHANGESET_STATUS_EXIT_CODE=$?

        if [ $CHANGESET_STATUS_EXIT_CODE -eq 0 ]; then
          echo "Changesets detected. Running version command."
          pnpm exec changeset version
          echo "Changeset version command finished. A commit should have been created."
          # Remember: You need a subsequent step to push this commit and tags.
        else
          echo "No changesets detected that require a version bump."
          exit 0 # Exit successfully if nothing to version
        fi

        # IMPORTANT: No publish or push steps here.
