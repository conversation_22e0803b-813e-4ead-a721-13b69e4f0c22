name: SMART-client Main Pipeline

# This triggers the pipeline on pushes to main (like after a merge)
trigger:
  branches:
    include:
      - main


variables:
  nodeVersion: '22.x'
  dockerfilePath: 'Dockerfile'
  imageTags: '$(Build.BuildId),latest'
  containerRegistryRepository: 'labdata/smart-client'
  containerRegistryServiceConnection: 'sc-crlabdata-test'
  pnpm_config_cache: $(Pipeline.Workspace)/.pnpm-store

stages:
  - stage: Build
    displayName: Build and Test
    jobs:
      - job: BuildAndTest
        timeoutInMinutes: 10
        displayName: Build, Test, and Publish Artifacts
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - template: templates/setup.yaml
            parameters:
              nodeVersion: $(nodeVersion)
              pnpm_config_cache: $(pnpm_config_cache)

          - template: templates/build.yaml

          - template: templates/test.yaml
            parameters:
            # disable e2e tests in pipeline
              runE2E: false

          # This template will run for both PRs and main branch builds.
          # We'll add a condition *inside* publish.yaml for steps that
          # should only run on main.
          - template: templates/publish.yaml
            parameters:
              artifactName: 'nextjs-app'
              # We pass this parameter, but the template will check
              # the build context (like Build.SourceBranch) if needed
              # for specific actions like updating changeset.
              updateChangeset: true

  # This Docker stage will ONLY run if the build was triggered by a push
  # directly to the 'main' branch (which includes merges).
  # It will SKIP on Pull Request builds.
  - stage: Docker
    displayName: Build and Push Docker Image
    dependsOn: Build
    condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
    jobs:
      - job: BuildImage
        timeoutInMinutes: 10
        displayName: Build Docker Image
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - task: DownloadBuildArtifacts@1
            displayName: Download Built App Artifact
            inputs:
              buildType: 'current'
              downloadType: 'single'
              artifactName: 'nextjs-app'
              downloadPath: '$(System.ArtifactsDirectory)'

          - task: Docker@2
            displayName: 'Build and push Docker image'
            inputs:
              command: 'buildAndPush'
              repository: $(containerRegistryRepository)
              dockerfile: $(dockerfilePath)
              containerRegistry: $(containerRegistryServiceConnection)
              tags: $(imageTags)
