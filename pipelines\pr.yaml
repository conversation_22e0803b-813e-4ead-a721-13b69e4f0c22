# This triggers the pipeline on Pull Requests targeting main
name: Next.js FHIR Client PR Pipeline

trigger: none
pr:
  branches:
    include:
      - main

pool:
  vmImage: 'ubuntu-latest'

variables:
  nodeVersion: '22.x'

stages:
  - stage: Build
    displayName: Build and Test
    jobs:
      - job: BuildAndTest
        displayName: Build, Test
        timeoutInMinutes: 10

        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - template: templates/setup.yaml
            parameters:
              nodeVersion: $(nodeVersion)

          - template: templates/build.yaml
            parameters:
              buildArguments: ''

          - template: templates/test.yaml
            parameters:
              runE2E: false
