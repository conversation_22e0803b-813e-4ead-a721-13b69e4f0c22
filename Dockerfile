FROM node:22-alpine AS base

# Install dependencies only when needed
FROM base AS deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* pnpm-workspace.yaml* ./

RUN if [ -f pnpm-lock.yaml ]; then echo "✅ pnpm lock file found."; \
  elif [ -f yarn.lock ]; then echo "ℹ️ yarn lock file found."; \
  elif [ -f package-lock.json ]; then echo "ℹ️ npm lock file found."; \
  else echo "⚠️ No standard lock file found."; fi

RUN if [ -f pnpm-workspace.yaml* ]; then echo "✅ pnpm-workspace file found."; \
  else echo "⚠️ No standard pnpm-workspace file found."; fi


RUN \
  if [ -f pnpm-lock.yaml ]; then npm install -g corepack@latest && corepack enable pnpm && pnpm i --frozen-lockfile; \
  elif [ -f package-lock.json ]; then npm ci; \
  elif  [ -f yarn.lock ]; then yarn --frozen-lockfile;\
  else echo "Lockfile not found." && exit 1; \
  fi


# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# # --- Biome linting ---
# # Ensure biome is installed as a dev dependency in your package.json
# RUN \
#   if [ -f pnpm-lock.yaml ]; then npm install -g corepack@latest && corepack enable pnpm && pnpm run lint:biome; \
#   elif [ -f package-lock.json ]; then npm run lint:biome; \
#   elif  [ -f yarn.lock ]; then yarn run lint:biome;\
#   else echo "Lint using biome not found." && exit 1; \
#   fi


# # --- Vitest unit testing ---
# # Ensure vitest is installed as a dev dependency in your package.json
# RUN \
#   if [ -f pnpm-lock.yaml ]; then npm install -g corepack@latest && corepack enable pnpm && pnpm run test; \
#   elif [ -f package-lock.json ]; then npm run test; \
#   elif  [ -f yarn.lock ]; then yarn run test;\
#   else echo "Vitest not found." && exit 1; \
#   fi

# ----------------------------------------

# set building the application to standalone
ENV BUILD_STANDALONE=true

# Build the application
RUN \
  if [ -f pnpm-lock.yaml ]; then npm install -g corepack@latest && corepack enable pnpm && pnpm run build; \
  elif [ -f package-lock.json ]; then npm run build; \
  elif  [ -f yarn.lock ]; then yarn run build;\
  else echo "Failed to build." && exit 1; \
  fi

# Disable Nextjs telemetry
RUN \
  if [ -f pnpm-lock.yaml ]; then npm install -g corepack@latest && corepack enable pnpm && pnpm exec next telemetry disable; \
  elif [ -f package-lock.json ]; then npx next telemetry disable; \
  elif  [ -f yarn.lock ]; then yarn next telemetry disable;\
  else echo "Failed to disable telemetry." && exit 1; \
  fi

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production
# Uncomment the following line in case you want to disable telemetry during runtime.
ENV NEXT_TELEMETRY_DISABLED=1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT=3000
# set hostname to localhost
ENV HOSTNAME="0.0.0.0"

# server.js is created by next build from the standalone output
# https://nextjs.org/docs/pages/api-reference/next-config-js/output
CMD ["node", "server.js"]
