import Link from "next/link";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { urls } from "./_lib/constants";

export default async function Home() {
	return (
		<div className="bg-muted flex min-h-svh flex-col items-center justify-center gap-6 p-6 md:p-10">
			<div className="flex w-full max-w-sm flex-col gap-6">
				<Card>
					<CardHeader className="text-center">
						<CardTitle className="text-xl">Välkommen till Labbsvar</CardTitle>
						<CardDescription>Noll läget, information om appen</CardDescription>
					</CardHeader>
					<CardContent>
						<ul className="space-y-1 text-center">
							{urls.map((item) => {
								return (
									<li key={item.url}>
										<Link className="underline" href={item.url}>
											{item.title}
										</Link>
									</li>
								);
							})}
						</ul>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
