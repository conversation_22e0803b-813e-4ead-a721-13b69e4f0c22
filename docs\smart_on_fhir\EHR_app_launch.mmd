sequenceDiagram
  participant P1 as SDV (EHR)
  participant P3 as Smart on fhir /launch.html
  participant P2 as Fhir server
  participant auth as Autentication
  participant ehr<PERSON><PERSON> as EHR FHIR server
  participant client as Smart on fhir /index.html

  participant client2 as Smart on fhir /index.html
  participant ehr as Fhir server


  
  P1 ->> P3: Provider launches the app with iss,patientId and practionerId
  Note left of P3: Redirect to App launch?launch=123&iss={fhir server}
  P3 ->> P2: App Gets /metadata ({fhir server}/metadata)
  P2 ->> P3: [Conformance statement including Oauth 2.0 endpoint]
  P3 ->> auth: App request authorization w/lauch + other
  Note left of auth: `Redirect to {authorization url} scope=launch state=123 etc..`
  auth ->> client: On Approval, redirect to /index.html
  client ->> auth: App exchange authorization token with access token
  auth ->> ehrFhir: Get fhir id from EHR
  ehrFhir ->> auth: Return PatientId, PractionerId, PractionerRoleId
  auth ->> client: access token granted with EHR fhir Ids
  client ->> P2: App access FHIR resources
  client --> ehrFhir: get fhir data for EHR patient,practioner,practionerRole
  Note over client, P2: Now the client has access to logged in Practioner,Patient from EHR
  client ->> P2: Using identifiers from EhrFHir server to get Fhir data for patient,practioner,practionerRole
  P2 ->> client: FHIR data retrieved
  Note left of client: App displays the data


Note over client2,ehr: Already done lauch and is authenticated
client2 ->> ehr: Get fhir data for EHR using /{resourceType&identifier={patientId}}
ehr ->> client2: FHIR data retrieved
