// app/api/fhir/[...path]/route.ts
import { type NextRequest, NextResponse } from "next/server";

// Set the target URL for your FHIR server
const FHIR_SERVER_URL = process.env.LOCAL_FHIRSERVER as string;

// Handler for GET requests
export async function GET(request: NextRequest) {
	const url = new URL(request.url);
	// Extract the path that was originally intended for the FHIR server
	// e.g., '/api/fhir/Patient/123' becomes '/Patient/123'
	const fhirPath = url.pathname.replace("/api/fhir", "");
	// Construct the full target URL for the FHIR server, including query parameters
	const targetUrl = `${FHIR_SERVER_URL}${fhirPath}${url.search}`;

	// Prepare headers to forward from the incoming request to the target server
	const headers = new Headers(request.headers);

	// Crucially, set the Host header to match the target server's expected host
	// This is important because the target server expects to be accessed via its own domain/host
	try {
		headers.set("Host", new URL(FHIR_SERVER_URL).host);
	} catch (error) {
		console.error("Failed to parse FHIR_SERVER_URL for Host header:", error);
		// Handle error appropriately, perhaps return a 500 response early
		return NextResponse.json(
			{ error: "Invalid FHIR server URL configured" },
			{ status: 500 },
		);
	}

	// Remove browser-specific headers that don't make sense server-to-server or can cause issues
	headers.delete("Origin");
	// Content-Length is automatically calculated by fetch for GET/HEAD with no body
	headers.delete("Content-Length");

	// Ensure the Authorization header is passed if available (important for SMART)
	if (request.headers.has("Authorization")) {
		headers.set("Authorization", request.headers.get("Authorization") ?? "");
	} else {
		// Optionally remove the header if not present to avoid sending an empty one
		headers.delete("Authorization");
	}

	try {
		// Make the request from the Next.js server to the target FHIR server
		const response = await fetch(targetUrl, {
			method: request.method, // This will be 'GET'
			headers: headers, // Forward the prepared headers
			// GET requests do not have a body, so we don't include request.body
			redirect: "manual", // Control how redirects are handled
			// cache: 'no-store', // Useful during development to ensure fresh data
		});

		// Copy relevant headers from the FHIR server's response back to the client
		const responseHeaders = new Headers(response.headers);

		// You might want to explicitly add CORS headers here if they aren't handled by the proxy
		// or if you want to ensure specific values for your client.
		// If using the App Router fetch approach, you ARE the server responding to the browser,
		// so you need to add CORS headers for your Next.js app's origin (http://localhost:3000).
		responseHeaders.set("Access-Control-Allow-Origin", "*"); // Or specify your client origin: 'http://localhost:3000'
		responseHeaders.set(
			"Access-Control-Allow-Methods",
			"GET, POST, PUT, DELETE, OPTIONS",
		); // Include all methods your proxy will support
		responseHeaders.set(
			"Access-Control-Allow-Headers",
			"Content-Type, Authorization",
		); // Include headers your client sends

		// Return the FHIR server's response body and status back to the client
		return new NextResponse(response.body, {
			status: response.status,
			statusText: response.statusText,
			headers: responseHeaders, // Include headers from the FHIR server's response
		});
	} catch (error) {
		console.error(`Proxy GET error for ${targetUrl}:`, error);
		// Return an appropriate error response to the client
		return NextResponse.json(
			{ error: "Failed to fetch data from FHIR server via proxy" },
			{ status: 500 },
		);
	}
}

// Example OPTIONS handler for CORS preflight (important!)
export async function OPTIONS() {
	const corsHeaders = {
		"Access-Control-Allow-Origin": "*", // Or specify your client origin
		"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
		"Access-Control-Allow-Headers": "Content-Type, Authorization", // Add any custom headers your client sends
		"Access-Control-Max-Age": "86400", // Cache preflight response for 24 hours
	};
	return new NextResponse(null, { status: 204, headers: corsHeaders });
}
