import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import { getFhirParams } from "../fhirParams.action";

describe("getFhirParams", () => {
	// Store original environment variables
	const originalEnv = { ...process.env };

	beforeEach(() => {
		// Set up test environment variables
		vi.stubEnv("IIS", "https://test-fhir-server.org/fhir");
		vi.stubEnv("CLIENTID", "test-client-id");
		vi.stubEnv("SCOPE", "test-scope");
	});

	afterEach(() => {
		// Restore original environment variables
		process.env = { ...originalEnv };
		vi.unstubAllEnvs();
	});

	it("should return FHIR parameters from environment variables", async () => {
		// Call the function
		const result = await getFhirParams();

		// Verify the result contains the expected values
		expect(result).toEqual({
			iss: "https://test-fhir-server.org/fhir",
			clientId: "test-client-id",
			scope: "test-scope",
		});
	});

	it("should handle missing environment variables", async () => {
		// Clear the environment variables
		vi.unstubAllEnvs();

		// Call the function
		const result = await getFhirParams();

		// Verify the result contains undefined values for missing env vars
		expect(result).toEqual({
			iss: undefined,
			clientId: undefined,
			scope: undefined,
		});
	});

	it("should handle partial environment variables", async () => {
		// Clear all environment variables first
		vi.unstubAllEnvs();

		// Set only one environment variable
		vi.stubEnv("IIS", "https://partial-test-server.org/fhir");

		// Call the function
		const result = await getFhirParams();

		// Verify the result contains the expected values
		expect(result).toEqual({
			iss: "https://partial-test-server.org/fhir",
			clientId: undefined,
			scope: undefined,
		});
	});
});
