"use client";
import { redirect } from "next/navigation";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { authClient } from "@/lib/auth-client";
import { cn } from "@/lib/utils";
import { Button } from "./ui/button";

// import { signIn } from "@/server/

export function LoginForm({
	className,
	...props
}: React.ComponentProps<"div">) {
	const signInWithMS = async () => {
		try {
			await authClient.signIn.social({
				provider: "microsoft",
				callbackURL: "/standalone",
			});
		} catch (error) {
			console.error(error);

			redirect("/error");
		}
	};

	return (
		<div className={cn("flex flex-col gap-6", className)} {...props}>
			<Card>
				<CardHeader className="text-center">
					<CardTitle className="text-xl">Välkomen till Labbsvar</CardTitle>
					<CardDescription>Historiska och nya <PERSON></CardDescription>
				</CardHeader>
				<CardContent>
					<div className="grid gap-6">
						<div className="flex flex-col gap-4">
							<Button
								type="button"
								variant="outline"
								className="w-full"
								onClick={signInWithMS}
							>
								Logga in med MS entra
							</Button>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
