sequenceDiagram
  participant fhir as Fhir server
  participant index as Smart on fhir /index

  note over index: Is Authenticated and authentication server provide practionerId
  index ->> fhir: Get PractionerRoles
  index ->> index: list of PractionerRoles
  index ->> index: select PractionerRole
  index ->> index: ask for Patient ID
  index ->> fhir: Get Patient
  index ->> fhir: Get Practioner
  index ->> fhir: Get PractionerRole
  index ->> index: display data


